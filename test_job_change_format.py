#!/usr/bin/env python3
"""
Test script to verify the "n years m months" format for average job change
Tests the new human-readable format for job change intervals
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce.settings')
django.setup()

# Now import Django components
from ecom.ResumeParser.export_utils import ExportUtils

def test_job_change_format():
    """Test the new "n years m months" format for average job change"""
    
    print("🧪 Testing Average Job Change Format: 'n years m months'")
    print("=" * 70)
    
    # Initialize export utils with a test job code
    export_utils = ExportUtils("TEST_JOB_001")
    
    # Test cases for the new format
    test_cases = [
        {
            "name": "Fresher Candidate",
            "fresher": True,
            "first_job_start_year": 2024,
            "last_job_end_year": 2024,
            "total_jobs_count": 0,
            "expected_result": "No job changes",
            "description": "Freshers should show 'No job changes'"
        },
        {
            "name": "Single Job Employee",
            "fresher": False,
            "first_job_start_year": 2019,
            "last_job_end_year": 2024,
            "total_jobs_count": 1,
            "expected_result": "No job changes",
            "description": "Single job should show 'No job changes'"
        },
        {
            "name": "Job Change Every 3 Years",
            "fresher": False,
            "first_job_start_year": 2018,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "3 years",
            "description": "2 changes over 6 years = 3 years per change"
        },
        {
            "name": "Job Change Every 2 Years",
            "fresher": False,
            "first_job_start_year": 2020,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "2 years",
            "description": "2 changes over 4 years = 2 years per change"
        },
        {
            "name": "Job Change Every 1.5 Years",
            "fresher": False,
            "first_job_start_year": 2021,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "1 year 6 months",
            "description": "2 changes over 3 years = 1.5 years per change"
        },
        {
            "name": "Job Change Every 2.5 Years",
            "fresher": False,
            "first_job_start_year": 2019,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "2 years 6 months",
            "description": "2 changes over 5 years = 2.5 years per change"
        },
        {
            "name": "Frequent Job Changes (Every 8 Months)",
            "fresher": False,
            "first_job_start_year": 2022,
            "last_job_end_year": 2024,
            "total_jobs_count": 4,
            "expected_result": "8 months",
            "description": "3 changes over 2 years = 8 months per change"
        },
        {
            "name": "Very Frequent Changes (Every 6 Months)",
            "fresher": False,
            "first_job_start_year": 2023,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "6 months",
            "description": "2 changes over 1 year = 6 months per change"
        },
        {
            "name": "Long Career with Few Changes",
            "fresher": False,
            "first_job_start_year": 2004,
            "last_job_end_year": 2024,
            "total_jobs_count": 4,
            "expected_result": "6 years 8 months",
            "description": "3 changes over 20 years = 6.67 years per change"
        },
        {
            "name": "Same Year Multiple Jobs",
            "fresher": False,
            "first_job_start_year": 2024,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": "6 months",
            "description": "2 changes in 1 year = 6 months per change"
        },
        {
            "name": "Invalid Data - Null Values",
            "fresher": False,
            "first_job_start_year": 'null',
            "last_job_end_year": 'null',
            "total_jobs_count": 'null',
            "expected_result": "No job changes",
            "description": "Null values should return 'No job changes'"
        }
    ]
    
    all_tests_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # Calculate average job change in new format
            result = export_utils.getAverageJobChange(
                test_case['fresher'],
                test_case['first_job_start_year'],
                test_case['last_job_end_year'],
                test_case['total_jobs_count']
            )
            
            print(f"  Input:")
            print(f"    Fresher: {test_case['fresher']}")
            print(f"    First Job Start: {test_case['first_job_start_year']}")
            print(f"    Last Job End: {test_case['last_job_end_year']}")
            print(f"    Total Jobs: {test_case['total_jobs_count']}")
            print(f"  Result: '{result}'")
            print(f"  Expected: '{test_case['expected_result']}'")
            print(f"  Description: {test_case['description']}")
            
            # Check if result matches expected
            if result == test_case['expected_result']:
                print(f"  ✅ TEST PASSED")
            else:
                print(f"  ❌ TEST FAILED - Expected '{test_case['expected_result']}', got '{result}'")
                all_tests_passed = False
                
        except Exception as e:
            print(f"  ❌ Test failed with exception: {str(e)}")
            all_tests_passed = False
    
    print("\n" + "=" * 70)
    if all_tests_passed:
        print("🎉 ALL JOB CHANGE FORMAT TESTS PASSED!")
        print("✅ Function correctly returns 'n years m months' format")
        print("✅ Handles all edge cases and invalid data properly")
        print("✅ Provides human-readable job change intervals")
    else:
        print("❌ SOME TESTS FAILED")
        print("⚠️  Job change format needs debugging")
    
    return all_tests_passed

def demonstrate_format_examples():
    """Demonstrate various format examples"""
    
    print("\n📊 Job Change Format Examples")
    print("=" * 50)
    
    export_utils = ExportUtils("TEST_JOB_001")
    
    examples = [
        {
            "profile": "Very Stable Employee",
            "start": 2015,
            "end": 2024,
            "jobs": 2,
            "story": "One job change in 9 years"
        },
        {
            "profile": "Moderate Job Changer",
            "start": 2018,
            "end": 2024,
            "jobs": 4,
            "story": "3 job changes over 6 years"
        },
        {
            "profile": "Frequent Job Changer",
            "start": 2021,
            "end": 2024,
            "jobs": 5,
            "story": "4 job changes in 3 years"
        },
        {
            "profile": "Job Hopper",
            "start": 2022,
            "end": 2024,
            "jobs": 6,
            "story": "5 job changes in 2 years"
        },
        {
            "profile": "Career Starter",
            "start": 2023,
            "end": 2024,
            "jobs": 2,
            "story": "1 job change in first year"
        }
    ]
    
    for example in examples:
        result = export_utils.getAverageJobChange(
            False, example['start'], example['end'], example['jobs']
        )
        
        career_years = example['end'] - example['start']
        job_changes = example['jobs'] - 1
        
        print(f"\n👤 {example['profile']}")
        print(f"   Story: {example['story']}")
        print(f"   Career: {example['start']}-{example['end']} ({career_years} years)")
        print(f"   Jobs: {example['jobs']} ({job_changes} changes)")
        print(f"   Average Time Between Changes: {result}")
        
        # Provide assessment
        if result == "No job changes":
            assessment = "Very stable - No job changes"
        elif "year" in result and "month" not in result:
            years = int(result.split()[0])
            if years >= 5:
                assessment = "Very stable - Long tenure per job"
            elif years >= 3:
                assessment = "Stable - Good job tenure"
            elif years >= 2:
                assessment = "Moderate - Reasonable job changes"
            else:
                assessment = "Frequent - Short job tenure"
        elif "month" in result and "year" not in result:
            assessment = "Very frequent - High job turnover"
        else:
            assessment = "Mixed - Moderate job changes"
        
        print(f"   Assessment: {assessment}")

def show_format_benefits():
    """Show benefits of the new format"""
    
    print("\n💡 Benefits of 'n years m months' Format")
    print("=" * 50)
    
    print("\n✅ Human-Readable:")
    print("   • '2 years 6 months' is clearer than '0.4 changes/year'")
    print("   • Intuitive understanding of job stability")
    print("   • Easy to communicate to non-technical stakeholders")
    
    print("\n✅ Business-Friendly:")
    print("   • HR teams can easily understand tenure patterns")
    print("   • Managers can assess retention risk")
    print("   • Clear expectations for job stability")
    
    print("\n✅ Precise Information:")
    print("   • Shows exact time between job changes")
    print("   • Handles both years and months accurately")
    print("   • Provides actionable insights for hiring decisions")
    
    print("\n📈 Interpretation Guide:")
    print("   • 'No job changes' → Very stable or fresher")
    print("   • '5+ years' → Very stable, good for long-term roles")
    print("   • '2-5 years' → Stable, normal career progression")
    print("   • '1-2 years' → Moderate changes, growth-oriented")
    print("   • '< 1 year' → Frequent changes, potential retention risk")

if __name__ == "__main__":
    print("🎯 Job Change Format Test Suite")
    print("Testing new 'n years m months' format for average job change")
    print()
    
    # Test the new format
    test_passed = test_job_change_format()
    
    # Show format examples
    demonstrate_format_examples()
    
    # Show benefits
    show_format_benefits()
    
    print("\n💡 What was implemented:")
    print("• Changed output from decimal (0.33) to human-readable format")
    print("• Added proper singular/plural handling for years and months")
    print("• Handles edge cases like 'No job changes' and 'Less than 1 month'")
    print("• Provides clear, actionable information for HR decisions")
    print("• Maintains all existing validation and error handling")
    
    if test_passed:
        print("\n🎉 JOB CHANGE FORMAT IS PERFECT!")
        print("✅ Function now returns 'n years m months' format as requested")
        print("✅ Human-readable and business-friendly output")
        print("✅ Perfect for resume analysis and HR decision making")
    else:
        print("\n⚠️  Some tests failed - please check the implementation")

import requests
import json
import datetime # Import the datetime module to get the current year

# --- Configuration ---
# Change this to the actual URL where your Ollama instance is running
OLLAMA_API_URL = "http://localhost:11434/api/generate"

# The model you want to test (e.g., "llama3", "llama3:8b", "mixtral")
MODEL_TO_TEST = "llama3.2:3b"

# --- Sample Data ---
# A resume with both internships and professional jobs to test the logic
SAMPLE_RESUME_TEXT = "<NAME_EMAIL>  +91 **********  Pune, Maharashtra PROFESSIONAL SUMMARY Senior Financial Analyst with 10+ years in banking sector, currently transitioning to Python development. Strong analytical skills and recent Python certification. Seeking to leverage domain expertise with technical skills. EDUCATION MBA in Finance IIM Indore (2014) CGPA: 8.5/10 Bachelor of Commerce Delhi University (2012) CGPA: 7.8/10 TECHNICAL SKILLS Python (Certified), SQL, Excel, Financial Analysis, Data Analysis, Pandas, NumPy, Matplotlib, Git, Problem Solving, Project Management PROJECTS Financial Data Analysis Tool Built Python tool for automated financial reporting and analysis Technologies: Python, Pandas, Matplotlib, SQLite Risk Assessment Dashboard Created dashboard for loan risk assessment using Python Technologies: Python, Streamlit, MySQL PROFESSIONAL EXPERIENCE Senior Financial Analyst HDFC Bank Apr 2018 - Present Led financial analysis and reporting for corporate banking division Financial Analyst ICICI Bank Jul 2014 - Mar 2018 Performed credit analysis and risk assessment for loan portfolio ACHIEVEMENTS  Python for Data Science Certification  Reduced reporting time by 60 using Python automation  Managed portfolio worth ¹500Cr+"


# --- Prompts ---

# Step 1: A simple prompt to filter out everything except professional experience
FILTER_PROMPT_TEMPLATE = """
Your task is to act as a text filter. From the resume text provided, extract and output ONLY the text related to professional, full-time work experience. You MUST EXCLUDE all internships, student projects, and educational entries. If no professional experience is found, respond with the exact phrase "No professional experience found."

RESUME TEXT:
{resume_text}
"""

# Step 2: A simple prompt to extract data from the CLEANED text
# FIXED: This prompt is now more direct and procedural for smaller models.
EXTRACTION_PROMPT_TEMPLATE = """
You are an expert data extractor. Follow these steps precisely using the text provided under "CLEANED EXPERIENCE TEXT":
1. Count the total number of distinct job entries. This is the "total_jobs_count".
2. Find the start year of the LAST job entry listed in the text. This is the "first_job_start_year".
3. Find the end year of the FIRST job entry listed in the text. If this end date is "Present", you MUST use {current_year}. This is the "last_job_end_year".
4. Format these three pieces of data into a single, valid JSON object and nothing else.

### JSON OUTPUT STRUCTURE:
{{
  "first_job_start_year": <number>,
  "last_job_end_year": <number>,
  "total_jobs_count": <number>
}}

### CLEANED EXPERIENCE TEXT:
{cleaned_text}
"""

def query_ollama(prompt: str, expect_json: bool = False) -> str:
    """
    Sends a prompt to the local Ollama API and returns the model's response.
    If expect_json is True, it will ask the model to format the output as JSON.
    """
    print(f"\n--- Sending Prompt to {MODEL_TO_TEST} (Expect JSON: {expect_json}) ---")
    print(prompt)
    
    try:
        payload = {
            "model": MODEL_TO_TEST,
            "prompt": prompt,
            "stream": False,
        }
        if expect_json:
            payload["format"] = "json"
        
        response = requests.post(OLLAMA_API_URL, json=payload)
        response.raise_for_status()
        
        response_data = response.json()
        model_output = response_data.get("response", "").strip()
        
        print(f"\n--- Raw Model Output ---")
        print(model_output)
        
        return model_output

    except requests.exceptions.RequestException as e:
        print(f"\n--- API Call Failed ---")
        print(f"Error connecting to Ollama at {OLLAMA_API_URL}.")
        print(f"Please make sure Ollama is running and the URL is correct.")
        print(f"Error details: {e}")
        return None
    except json.JSONDecodeError:
        print("\n--- Error ---")
        print("Failed to decode the JSON response from the server.")
        return None


if __name__ == "__main__":
    print("Starting Resume Extraction Test...")

    # --- STEP 1: Filter the resume to get only professional experience ---
    print("\n==================== STEP 1: FILTERING RESUME ====================")
    filter_prompt = FILTER_PROMPT_TEMPLATE.format(resume_text=SAMPLE_RESUME_TEXT)
    
    # Make a real API call to the LLM to get the cleaned text
    cleaned_experience = query_ollama(filter_prompt, expect_json=False)

    if not cleaned_experience or "No professional experience found" in cleaned_experience:
        print("\nResult: No professional experience found. Test finished.")
    else:
        # --- STEP 2: Extract structured data from the cleaned text ---
        print("\n==================== STEP 2: EXTRACTING DATA ====================")
        
        current_year = datetime.datetime.now().year
        
        extraction_prompt = EXTRACTION_PROMPT_TEMPLATE.format(
            cleaned_text=cleaned_experience,
            current_year=current_year
        )
        
        # Make the second API call to get the structured JSON data
        json_output_str = query_ollama(extraction_prompt, expect_json=True)
        
        if json_output_str:
            try:
                extracted_data = json.loads(json_output_str)
                print("\n--- Final Parsed Data ---")
                print(json.dumps(extracted_data, indent=2))
                
                # Verification
                assert extracted_data["first_job_start_year"] == 2014
                assert extracted_data["last_job_end_year"] == current_year
                assert extracted_data["total_jobs_count"] == 2
                print("\n✅ Verification Successful!")

            except (json.JSONDecodeError, KeyError, AssertionError) as e:
                print("\n--- Verification Failed ---")
                print("The model's output was not in the expected format or had incorrect values.")
                print(f"Error details: {e}")
        else:
            print("\nTest finished with errors.")

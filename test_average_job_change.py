#!/usr/bin/env python3
"""
Test script to demonstrate the improved getAverageJobChange function
Tests average job change calculation based on 12 months
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce.settings')
django.setup()

# Now import Django components
from ecom.ResumeParser.export_utils import ExportUtils

def test_average_job_change_calculation():
    """Test the improved average job change calculation"""
    
    print("🧪 Testing Average Job Change Calculation (Based on 12 Months)")
    print("=" * 70)
    
    # Initialize export utils with a test job code
    export_utils = ExportUtils("TEST_JOB_001")
    
    # Test cases for average job change calculation
    test_cases = [
        {
            "name": "Fresher Candidate",
            "fresher": True,
            "first_job_start_year": 2024,
            "last_job_end_year": 2024,
            "total_jobs_count": 0,
            "expected_result": 0,
            "description": "Freshers should always return 0"
        },
        {
            "name": "Stable Employee (1 Job, 5 Years)",
            "fresher": False,
            "first_job_start_year": 2019,
            "last_job_end_year": 2024,
            "total_jobs_count": 1,
            "expected_result": 0,
            "description": "1 job = 0 job changes"
        },
        {
            "name": "Moderate Job Changer (3 Jobs, 6 Years)",
            "fresher": False,
            "first_job_start_year": 2018,
            "last_job_end_year": 2024,
            "total_jobs_count": 3,
            "expected_result": 0.33,
            "description": "2 job changes over 6 years = 0.33 changes/year"
        },
        {
            "name": "Frequent Job Changer (5 Jobs, 4 Years)",
            "fresher": False,
            "first_job_start_year": 2020,
            "last_job_end_year": 2024,
            "total_jobs_count": 5,
            "expected_result": 1.0,
            "description": "4 job changes over 4 years = 1.0 change/year"
        },
        {
            "name": "Very Frequent Job Changer (6 Jobs, 3 Years)",
            "fresher": False,
            "first_job_start_year": 2021,
            "last_job_end_year": 2024,
            "total_jobs_count": 6,
            "expected_result": 1.67,
            "description": "5 job changes over 3 years = 1.67 changes/year"
        },
        {
            "name": "Long Career, Stable (4 Jobs, 20 Years)",
            "fresher": False,
            "first_job_start_year": 2004,
            "last_job_end_year": 2024,
            "total_jobs_count": 4,
            "expected_result": 0.15,
            "description": "3 job changes over 20 years = 0.15 changes/year"
        },
        {
            "name": "Invalid Data - Null Values",
            "fresher": False,
            "first_job_start_year": 'null',
            "last_job_end_year": 'null',
            "total_jobs_count": 'null',
            "expected_result": 0,
            "description": "Null values should return 0"
        },
        {
            "name": "Invalid Data - Zero Jobs",
            "fresher": False,
            "first_job_start_year": 2020,
            "last_job_end_year": 2024,
            "total_jobs_count": 0,
            "expected_result": 0,
            "description": "Zero jobs should return 0"
        },
        {
            "name": "Edge Case - Same Year Start/End",
            "fresher": False,
            "first_job_start_year": 2024,
            "last_job_end_year": 2024,
            "total_jobs_count": 2,
            "expected_result": 1.0,
            "description": "2 jobs in same year = 1 change in 1 year = 1.0"
        }
    ]
    
    all_tests_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # Calculate average job change
            result = export_utils.getAverageJobChange(
                test_case['fresher'],
                test_case['first_job_start_year'],
                test_case['last_job_end_year'],
                test_case['total_jobs_count']
            )
            
            print(f"  Input:")
            print(f"    Fresher: {test_case['fresher']}")
            print(f"    First Job Start: {test_case['first_job_start_year']}")
            print(f"    Last Job End: {test_case['last_job_end_year']}")
            print(f"    Total Jobs: {test_case['total_jobs_count']}")
            print(f"  Result: {result} job changes per year")
            print(f"  Expected: {test_case['expected_result']}")
            print(f"  Description: {test_case['description']}")
            
            # Check if result matches expected (with small tolerance for floating point)
            if abs(result - test_case['expected_result']) < 0.01:
                print(f"  ✅ TEST PASSED")
            else:
                print(f"  ❌ TEST FAILED - Expected {test_case['expected_result']}, got {result}")
                all_tests_passed = False
                
        except Exception as e:
            print(f"  ❌ Test failed with exception: {str(e)}")
            all_tests_passed = False
    
    print("\n" + "=" * 70)
    if all_tests_passed:
        print("🎉 ALL AVERAGE JOB CHANGE TESTS PASSED!")
        print("✅ Function correctly calculates job changes per year")
        print("✅ Handles freshers, stable employees, and job hoppers")
        print("✅ Properly validates input data and handles edge cases")
    else:
        print("❌ SOME TESTS FAILED")
        print("⚠️  Average job change calculation needs debugging")
    
    return all_tests_passed

def demonstrate_job_change_interpretation():
    """Demonstrate how to interpret job change values"""
    
    print("\n📊 Job Change Interpretation Guide")
    print("=" * 50)
    
    interpretations = [
        (0.0, "No job changes - Very stable employee or fresher"),
        (0.1, "Very stable - Changes job every 10 years"),
        (0.2, "Stable - Changes job every 5 years"),
        (0.33, "Moderate - Changes job every 3 years"),
        (0.5, "Moderate - Changes job every 2 years"),
        (1.0, "Frequent - Changes job every year"),
        (1.5, "Very frequent - Changes job every 8 months"),
        (2.0, "Extremely frequent - Changes job every 6 months"),
    ]
    
    print("\nJob Change Rate Interpretation:")
    print("-" * 40)
    for rate, interpretation in interpretations:
        print(f"  {rate:4.1f} changes/year → {interpretation}")
    
    print("\n💡 Business Insights:")
    print("• 0.0 - 0.2: Very stable, good for long-term roles")
    print("• 0.2 - 0.5: Moderate stability, normal career progression")
    print("• 0.5 - 1.0: Frequent changes, may indicate growth-seeking or instability")
    print("• 1.0+: Very frequent changes, potential red flag for retention")

def calculate_real_world_examples():
    """Calculate job change rates for real-world scenarios"""
    
    print("\n🌍 Real-World Examples")
    print("=" * 40)
    
    export_utils = ExportUtils("TEST_JOB_001")
    
    examples = [
        {
            "profile": "Software Engineer - Stable Career",
            "start": 2015,
            "end": 2024,
            "jobs": 3,
            "story": "Started at startup, moved to mid-size company, now at FAANG"
        },
        {
            "profile": "Consultant - High Mobility",
            "start": 2020,
            "end": 2024,
            "jobs": 6,
            "story": "Project-based consulting with frequent client changes"
        },
        {
            "profile": "Senior Manager - Strategic Moves",
            "start": 2010,
            "end": 2024,
            "jobs": 4,
            "story": "Climbed corporate ladder with strategic job changes"
        },
        {
            "profile": "Fresh Graduate - Early Career",
            "start": 2022,
            "end": 2024,
            "jobs": 2,
            "story": "Started with internship, moved to full-time role"
        }
    ]
    
    for example in examples:
        rate = export_utils.getAverageJobChange(
            False, example['start'], example['end'], example['jobs']
        )
        
        career_years = example['end'] - example['start'] + 1
        job_changes = example['jobs'] - 1
        
        print(f"\n👤 {example['profile']}")
        print(f"   Story: {example['story']}")
        print(f"   Career: {example['start']}-{example['end']} ({career_years} years)")
        print(f"   Jobs: {example['jobs']} ({job_changes} changes)")
        print(f"   Rate: {rate} job changes per year")
        
        if rate == 0:
            stability = "No changes"
        elif rate < 0.2:
            stability = "Very stable"
        elif rate < 0.5:
            stability = "Moderately stable"
        elif rate < 1.0:
            stability = "Frequent changes"
        else:
            stability = "Very frequent changes"
        
        print(f"   Assessment: {stability}")

if __name__ == "__main__":
    print("🎯 Average Job Change Calculation Test Suite")
    print("Testing improved calculation based on 12-month periods")
    print()
    
    # Test the function
    test_passed = test_average_job_change_calculation()
    
    # Show interpretation guide
    demonstrate_job_change_interpretation()
    
    # Show real-world examples
    calculate_real_world_examples()
    
    print("\n💡 What was improved:")
    print("• Added proper input validation and error handling")
    print("• Fixed calculation to show job changes per year (not job duration)")
    print("• Added comprehensive documentation and comments")
    print("• Handles edge cases like same year start/end dates")
    print("• Returns meaningful values for business decision making")
    
    if test_passed:
        print("\n🎉 AVERAGE JOB CHANGE CALCULATION IS WORKING PERFECTLY!")
        print("✅ Function now correctly calculates job change frequency")
        print("✅ Based on 12-month periods as requested")
        print("✅ Provides meaningful insights for recruitment decisions")
    else:
        print("\n⚠️  Some tests failed - please check the implementation")

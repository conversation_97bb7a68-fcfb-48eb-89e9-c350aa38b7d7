{% extends 'ecom/admin_base.html' %}
{% load static %}

{% block scripts %}
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

  <script>
  $(document).ready(function() {

    {% if analysis_started %}
      var myModal = new bootstrap.Modal(document.getElementById('myModal'));
      myModal.show();

      $.ajax({
        type: "POST",
        url: "http://127.0.0.1:8000/analyse-pdfs/",
        data: JSON.stringify({ job_id: {{job_id}} }),
        contentType: "application/json",
        success: function (data) {
            /*document.getElementById("resp").innerText = data.res;
            const imgTag = document.getElementById("preview");
            imgTag.src = "data:image/jpeg;base64," + data.parsed_image;*/

            if(data.analyseSuccess) {
              
              setTimeout(() => {
                myModal.hide();
                window.location.href = window.location.pathname;
              }, 1000);

            }


        },
        error: function (err) {
            console.error("Error:", err);
        },
        complete: function () {
            // Allow next frame after completion
        }
      });


    {% endif %}


    $('#openModalBtn').click(function() {
      /*var myModal = new bootstrap.Modal(document.getElementById('myModal'));
      myModal.show();*/
      window.location.href = window.location.pathname + "?startAnalysis=True";

    });
  });
</script>

{% endblock scripts %}



{% block content %}
<br><br><br>

<div class="container mt-4" id="container">
  <h2 class="mb-4 fw-bold text-primary d-flex justify-content-between">📂 Resume Analysis
    <button class="btn btn-primary btn-sm mt-auto" id="openModalBtn">New Analysis</button>
  </h2>

  {% if output_file_names %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      {% for filename in output_file_names %}
        <div class="col">
          <div class="card shadow-sm h-100 border-0 rounded-3">
            <div class="card-body bg-light d-flex flex-column justify-content-center align-items-start">
              <h5 class="card-title fw-semibold text-dark mb-2">
                <i class="bi bi-file-earmark-text-fill me-2"></i> {{ filename }}
              </h5>
              <a href="{{request.path}}/{{filename}}" class="btn btn-outline-primary btn-sm mt-auto">View Details</a>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="alert alert-info">No output files found.</div>
  {% endif %}
</div>
<br><br>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content border-0 shadow">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title mb-0" id="myModalLabel">
          Resume Analysis in Progress
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body d-flex flex-column align-items-center text-center py-4">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="fw-semibold mb-0">
          Started resume analysis for the job description.<br>
          Waiting for it to complete...
        </p>
      </div>
    </div>
  </div>
</div>


<style>
  #container {
  min-height: 70vh;
  height: auto;
}

</style>

{% endblock content %}

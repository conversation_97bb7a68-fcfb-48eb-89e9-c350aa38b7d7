from django.contrib import admin
from django.urls import path , include
from ecom import views
from django.contrib.auth.views import LoginView,LogoutView
from django.conf import settings
from django.conf.urls.static import static
urlpatterns = [
    path('admin/', admin.site.urls),
    path('',views.home_view,name=''),
    path('afterlogin', views.afterlogin_view,name='afterlogin'),
    path('logout', LogoutView.as_view(template_name='ecom/logout.html'),name='logout'),
    path('search', views.search_view,name='search'),

    path('adminclick', views.adminclick_view),
    path('adminlogin', LoginView.as_view(template_name='ecom/adminlogin.html'),name='adminlogin'),
    path('admin-dashboard', views.admin_dashboard_view,name='admin-dashboard'),

    path('view-customer', views.view_customer_view,name='view-customer'),
    path('delete-customer/<int:pk>', views.delete_customer_view,name='delete-customer'),
    path('update-customer/<int:pk>', views.update_customer_view,name='update-customer'),

    path('admin-products', views.admin_products_view,name='admin-products'),
    path('admin-add-product', views.admin_add_product_view,name='admin-add-product'),
    path('delete-product/<int:pk>', views.delete_product_view,name='delete-product'),
    path('update-product/<int:pk>', views.update_product_view,name='update-product'),

    path('admin-view-booking', views.admin_view_booking_view,name='admin-view-booking'),
    path('delete-order/<int:pk>', views.delete_order_view,name='delete-order'),
    path('update-order/<int:pk>', views.update_order_view,name='update-order'),


    path('customersignup', views.customer_signup_view),
    path('customerlogin', LoginView.as_view(template_name='ecom/customerlogin.html'),name='customerlogin'),
    path('customer-home', views.customer_home_view,name='customer-home'),
    path('my-order', views.my_order_view,name='my-order'),
    path('my-profile', views.my_profile_view,name='my-profile'),
    path('edit-profile', views.edit_profile_view,name='edit-profile'),
    path('download-invoice/<int:orderID>/<int:productID>', views.download_invoice_view,name='download-invoice'),


    path('add-to-cart/<int:pk>', views.add_to_cart_view,name='add-to-cart'),
    path('cart', views.cart_view,name='cart'),
    path('remove-from-cart/<int:pk>', views.remove_from_cart_view,name='remove-from-cart'),
    path('customer-address', views.customer_address_view,name='customer-address'),
    path('payment-success', views.payment_success_view,name='payment-success'),
    path('' , include('paypal.standard.ipn.urls')),     # Paypal payments
    path('stripe-payment' , views.stripe_payment , name='stripe-payment'),
    path('phonepe-payment' , views.phonepe_payment , name='phonepe-payment'),
    path('gpay-payment/' , views.gpay_payment , name='gpay-payment'),
    path('validate-payment/' , views.validate_payment , name='validate-payment'),
    
    path('face-login/' , views.face_login , name='face-login'),
    path("get_res/" , views.get_res , name="get_res"),
    path("register_face/save_frame/" , views.save_frame , name="get_res"),
    
    path("create-new-job/" , views.create_new_job , name="create-new-job"),
    path("manage-jobs/" , views.manage_jobs , name="manage-jobs"),
    path("apply-job/<str:job_code>" , views.apply_job , name="apply-job"),
    path("job-detail/<str:job_code>" , views.job_detail , name="job-detail"),
    path("view-analysis/<str:job_code>" , views.view_analysis , name="view-analysis"),
    path("view-analysis/<str:job_code>/<str:filename>" , views.view_analysis_file , name="view-analysis-file"),
    
    path("analyse-pdfs/" , views.analyse_resumes , name="analyse-pdfs"),

    path("test/" , views.test , name="test"),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
